{"name": "@es-joy/jsdoccomment", "version": "0.52.0", "author": "<PERSON> <<EMAIL>>", "contributors": [], "description": "Maintained replacement for ESLint's deprecated SourceCode#getJSDocComment along with other jsdoc utilities", "license": "MIT", "keywords": ["ast", "comment", "estree", "jsdoc", "parser", "eslint", "sourcecode"], "type": "module", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./src/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs.cjs"}}}, "browserslist": ["defaults, not op_mini all"], "typedocOptions": {"dmtLinksService": {"GitHub": "https://github.com/es-joy/jsdoccomment", "NPM": "https://www.npmjs.com/package/@es-joy/jsdoccomment"}}, "repository": {"type": "git", "url": "git+https://github.com/es-joy/jsdoccomment.git"}, "bugs": {"url": "https://github.com/es-joy/jsdoccomment/issues"}, "homepage": "https://github.com/es-joy/jsdoccomment", "engines": {"node": ">=20.11.0"}, "dependencies": {"@types/estree": "^1.0.8", "@typescript-eslint/types": "^8.34.1", "comment-parser": "1.4.1", "esquery": "^1.6.0", "jsdoc-type-pratt-parser": "~4.1.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/preset-env": "^7.27.2", "@rollup/plugin-babel": "^6.0.4", "@types/esquery": "^1.5.4", "@types/estraverse": "^5.1.7", "@typescript-eslint/visitor-keys": "^8.34.1", "@typhonjs-build-test/esm-d-ts": "^0.3.0-next.9", "@typhonjs-typedoc/typedoc-pkg": "^0.3.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.29.0", "eslint-config-ash-nazg": "38.0.2", "eslint-plugin-jsdoc": "^51.1.1", "espree": "^10.4.0", "estraverse": "^5.3.0", "rollup": "^4.44.0", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vitest": "^3.2.4"}, "files": ["/dist", "/src", "CHANGES.md", "LICENSE-MIT.txt"], "scripts": {"build": "rollup -c && npm run types", "docs": "typedoc-pkg --api-link es", "eslint": "eslint .", "lint": "npm run eslint --", "open": "open ./coverage/index.html", "test": "npm run lint && npm run build && npm run test-cov", "test-ui": "vitest --ui --coverage", "test-cov": "vitest --coverage", "tsc": "tsc", "types": "esm-d-ts gen ./src/index.js --output ./dist/index.d.ts --emitCTS"}}