import { EventEnvelope, EventType, PersistedEventEnvelope } from "../event-store/event-store";

export interface UserProductState {
  id: string;
  hostUserId: string;
  ticketPrice: number;
  maxEntries: number;
  entries: { userId: string; count: number }[];
  status: "active" | "cancelled" | "completed";
  winnerUserId?: string;
  version: number;
}

export class UserProductAggregate {
  private constructor(private readonly state: UserProductState) {}

  public static create(initialState?: UserProductState): UserProductAggregate {
    const defaultState: UserProductState = {
      id: "",
      hostUserId: "",
      ticketPrice: 0,
      maxEntries: 0,
      entries: [],
      status: "active",
      version: 0
    };
    return new UserProductAggregate(initialState || defaultState);
  }

  public applyEvent(event: PersistedEventEnvelope): UserProductAggregate {
    const newState = this.evolveState(this.state, event);
    return new UserProductAggregate(newState);
  }

  private evolveState(currentState: UserProductState, event: PersistedEventEnvelope): UserProductState {
    switch (event.eventType) {
      case EventType.RaffleCreated:
        return {
          ...currentState,
          ...event.payload,
          status: "active",
          version: event.version
        };

      case EventType.TicketPurchased:
        return {
          ...currentState,
          entries: [
            ...currentState.entries,
            { userId: event.payload.userId, count: event.payload.ticketCount }
          ],
          version: event.version
        };

      case EventType.RaffleWinnerDrawn:
        return {
          ...currentState,
          winnerUserId: event.payload.userId,
          status: "completed",
          version: event.version
        };

      case EventType.RaffleRefundIssued:
        return {
          ...currentState,
          entries: currentState.entries.filter(e => e.userId !== event.payload.userId),
          version: event.version
        };

      case EventType.RaffleCancelled:
        return {
          ...currentState,
          status: "cancelled",
          version: event.version
        };

      default:
        return currentState;
    }
  }

  public static rehydrate(events: PersistedEventEnvelope[]): UserProductAggregate {
    return events.reduce(
      (agg, event) => agg.applyEvent(event),
      UserProductAggregate.create()
    );
  }

  public getState(): UserProductState {
    return { ...this.state };
  }

  // Command methods
  public purchaseTicket(userId: string, ticketCount: number): EventEnvelope {
    if (this.state.status !== "active") throw new Error("Raffle not active");
    
    const totalEntries = this.state.entries.reduce((sum, e) => sum + e.count, 0);
    if (totalEntries + ticketCount > this.state.maxEntries) {
      throw new Error("Not enough tickets available");
    }

    return {
      eventType: EventType.TicketPurchased,
      payload: {
        userId,
        ticketCount,
        totalCost: ticketCount * this.state.ticketPrice
      }
    };
  }

  public drawWinner(): EventEnvelope {
    if (this.state.status !== "active") throw new Error("Raffle not active");
    
    const totalEntries = this.state.entries.reduce((sum, e) => sum + e.count, 0);
    if (totalEntries < this.state.maxEntries) {
      throw new Error("Not all tickets sold");
    }

    // Create weighted array for fair selection
    const weightedEntries: string[] = [];
    this.state.entries.forEach(entry => {
      for (let i = 0; i < entry.count; i++) {
        weightedEntries.push(entry.userId);
      }
    });

    const randomIndex = Math.floor(Math.random() * weightedEntries.length);
    const winnerId = weightedEntries[randomIndex];

    return {
      eventType: EventType.RaffleWinnerDrawn,
      payload: { userId: winnerId }
    };
  }

  public cancelRaffle(): EventEnvelope {
    if (this.state.status !== "active") throw new Error("Raffle not active");

    return {
      eventType: EventType.RaffleCancelled,
      payload: {}
    };
  }

  public issueRefund(userId: string): EventEnvelope {
    if (this.state.status !== "active") throw new Error("Raffle not active");
    
    const userEntry = this.state.entries.find(e => e.userId === userId);
    if (!userEntry) throw new Error("User has no tickets");

    return {
      eventType: EventType.RaffleRefundIssued,
      payload: {
        userId,
        refundAmount: userEntry.count * this.state.ticketPrice
      }
    };
  }
}
